document.addEventListener('DOMContentLoaded', function() {
    console.log('Le site NB Driver est prêt.');

    const contactForm = document.querySelector('#contact-page form');

    if (contactForm) {
        contactForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Empêche l'envoi du formulaire pour la validation

            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const message = document.getElementById('message').value.trim();
            let isValid = true;
            
            if (name === '') {
                alert('Veuillez entrer votre nom.');
                isValid = false;
            }

            if (email === '') {
                alert('Veuillez entrer votre adresse email.');
                isValid = false;
            } else if (!validateEmail(email)) {
                alert('Veuillez entrer une adresse email valide.');
                isValid = false;
            }

            if (message === '') {
                alert('Veuillez écrire votre message.');
                isValid = false;
            }

            if (isValid) {
                alert('Merci pour votre message ! Nous vous répondrons bientôt.');
                // Ici, on pourrait ajouter le code pour envoyer le formulaire via AJAX
                // Par exemple : contactForm.submit();
                contactForm.reset(); // Réinitialise le formulaire
            }
        });
    }
});

function validateEmail(email) {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}
