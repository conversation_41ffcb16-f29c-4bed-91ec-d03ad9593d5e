@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Playfair+Display:wght@700&display=swap');

/* 
Thème Sombre Luxe
- Fond: #121212 (quasi-noir)
- Texte: #EAEAEA (blanc cassé)
- Accent: #D4AF37 (or)
- Police Titres: 'Playfair Display', serif
- Police Texte: 'Montserrat', sans-serif
*/

body {
    font-family: 'Montserrat', sans-serif;
    line-height: 1.8;
    margin: 0;
    padding: 0;
    color: #EAEAEA;
    background-color: #121212;
}

h1, h2, h3, h4 {
    font-family: 'Playfair Display', serif;
    color: #D4AF37;
}

a {
    color: #D4AF37;
    text-decoration: none;
    transition: color 0.3s;
}

a:hover {
    color: #fff;
}

/* Header et Navigation */
header {
    background: rgba(18, 18, 18, 0.8);
    backdrop-filter: blur(10px);
    padding: 1.5rem 0;
    border-bottom: 1px solid #333;
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 90%;
    margin: 0 auto;
}

nav .logo a {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 2.5rem;
}

nav ul li a {
    font-weight: bold;
    font-size: 1rem;
}

.btn {
    background-color: #D4AF37;
    color: #121212;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-weight: bold;
    transition: background-color 0.3s, color 0.3s;
    border: 2px solid #D4AF37;
}

.btn:hover {
    background-color: transparent;
    color: #D4AF37;
}

/* Sections */
main section {
    padding: 6rem 2rem;
    width: 90%;
    margin: 0 auto;
    text-align: center;
    border-bottom: 1px solid #222;
}

main section:last-child {
    border-bottom: none;
}

h2 {
    font-size: 3rem;
    margin-bottom: 3rem;
}

/* Section Héros */
#hero {
    background: url('https://images.unsplash.com/photo-1533134486753-c833f0ed4866?q=80&w=2070&auto=format&fit=crop') no-repeat center center/cover;
    color: #fff;
    padding: 8rem 0;
    position: relative;
}

#hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
}

#hero > * {
    position: relative;
    z-index: 2;
}

#hero h1 {
    color: #fff;
    font-size: 4rem;
}

.booking-form {
    background: rgba(0,0,0,0.5);
    padding: 2.5rem;
    border-radius: 8px;
    display: inline-block;
    border: 1px solid #D4AF37;
}

.booking-form input[type="text"] {
    background: #333;
    border: 1px solid #555;
    color: #fff;
    padding: 1rem;
    border-radius: 5px;
    width: 250px;
}

/* Section Services */
.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.service-item {
    background: #1a1a1a;
    padding: 2.5rem;
    border-radius: 8px;
    border: 1px solid #333;
    transition: transform 0.3s, border-color 0.3s;
}

.service-item:hover {
    transform: translateY(-10px);
    border-color: #D4AF37;
}

/* Section "Pourquoi nous choisir ?" */
.features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
    text-align: left;
}

/* Section Flotte */
#fleet img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

/* Formulaires */
.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #555;
    border-radius: 5px;
    box-sizing: border-box;
    background: #333;
    color: #fff;
}

.contact-form-container {
    max-width: 700px;
    margin: 0 auto;
    background: #1a1a1a;
    padding: 3rem;
    border-radius: 8px;
}

/* Footer */
footer {
    background: #000;
    color: #aaa;
    text-align: center;
    padding: 3rem 0;
    margin-top: 4rem;
}

/* Media Queries */
@media (max-width: 768px) {
    h2 { font-size: 2.5rem; }
    #hero h1 { font-size: 3rem; }
    nav { flex-direction: column; gap: 1.5rem; }
    nav ul { padding: 0; }
    .features { grid-template-columns: 1fr; }
}
